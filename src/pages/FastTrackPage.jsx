import { useRef } from "react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { FastTrackForm } from "../components/FastTrackForm/FastTrackForm";
import { useValidateRepApi } from "../hooks/useValidateRepApi";
import { LoadingOverlay } from "../components/shared/LoadingSpinner";
import { useAppStorage } from "../hooks/useAppStorage";
import { PreQualifyExplainer, QuickLinksSection } from "./PreQualifyPage";
import { trackCustomEvent } from "../utils/analytics";

/**
 * Fast Track Page Component
 * Validates utm_rep parameter and shows fast track form if valid
 * @returns {JSX.Element}
 */
export const FastTrackPage = () => {
  const navigate = useNavigate();
  const { utmParams, fastTrackFormParams } = useAppStorage();
  const { validateRep, isLoading } = useValidateRepApi();
  const [isValidating, setIsValidating] = useState(true);
  const formInitialized = useRef(false);

  useEffect(() => {
    if (formInitialized.current) return;

    const checkRepValidation = async () => {
      // Check if utm_rep parameter exists
      const utmRep = utmParams?.utm_rep;

      if (!utmRep) {
        // No utm_rep parameter, redirect to prequalify page
        navigate("/", { replace: true });
        return;
      }

      try {
        // Validate the rep parameter
        const validationResult = await validateRep(utmRep);

        if (!validationResult?.valid) {
          // Invalid rep, redirect to prequalify page
          navigate("/", { replace: true });
          return;
        } else {
          if (!formInitialized.current) {
            trackCustomEvent("fast_track", true);
            formInitialized.current = true;
          }
        }

        setIsValidating(false);
      } catch (error) {
        // Error validating rep, redirect to prequalify page
        console.error("Error validating rep:", error);
        navigate("/", { replace: true });
      }
    };

    checkRepValidation();
  }, [utmParams, validateRep, navigate]);

  // Show loading while validating
  if (isValidating || isLoading) {
    return <LoadingOverlay />;
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col lg:flex-row lg:space-x-8 py-4 lg:py-8">
        <div className="w-full lg:w-1/3 mb-6 lg:mb-0">
          <PreQualifyExplainer />
        </div>

        <div className="w-full lg:w-2/3">
          <FastTrackForm fastTrackParams={fastTrackFormParams} />
        </div>
      </div>
      <QuickLinksSection />
    </div>
  );
};
