import { useRef, useState, useEffect } from "react";
import { ErrorModal } from "../shared/ErrorModal";
import { LoadingOverlay } from "../shared/LoadingSpinner";
import { useNavigate } from "react-router-dom";
import { useAppStorage } from "../../hooks/useAppStorage.js";
import { useFastTrackApi } from "../../hooks/useFastTrackApi.js";

import { useForm } from "react-hook-form";
import { defaultFastTrackValues } from "../../utils/consts.js";
import { logger } from "../../utils/logger.js";
import { FormField } from "../shared/FormField.jsx";
import { PhoneField } from "../shared/PhoneField.jsx";
import { SelectField } from "../shared/SelectField.jsx";
import { CheckboxField } from "../shared/CheckboxField.jsx";
import { validationSchema } from "../../utils/validationSchema";
import DataSecuredCard from "../shared/DataSecuredCard.jsx";
import { trackCustomEvent } from "../../utils/analytics.js";

// FICO score range options (same as prequalification form)
const ficoOptions = [
  { value: "780-850", label: "Excellent (780-850)" },
  { value: "700-780", label: "Very Good (700-780)" },
  { value: "625-700", label: "Good (625-700)" },
  { value: "550-625", label: "Fair (550-625)" },
  { value: "300-550", label: "Poor (300-550)" },
];

/**
 * Fast Track form component with single step process
 * @param {Object} fastTrackParams - URL parameters for prefilling the form
 * @returns {JSX.Element}
 */
export const FastTrackForm = ({ fastTrackParams }) => {
  return <FastTrackFormContainer fastTrackParams={fastTrackParams} />;
};

/**
 * Container component that handles all the logic and state management for the fast track form
 * @param {Object} fastTrackParams - URL parameters for prefilling the form
 * @returns {JSX.Element}
 */
const FastTrackFormContainer = ({ fastTrackParams }) => {
  const {
    applicationId: appId,
    setApplicationId: storeAppID,
    fastTrackForm: savedFields,
    setFastTrackForm: storeFormFields,
    clearAllData,
  } = useAppStorage();

  if (appId) {
    clearAllData();
  }

  const {
    error: errorMessage,
    errorId,
    submitFastTrack,
    status,
  } = useFastTrackApi();

  const [showErrorModal, setShowErrorModal] = useState(false);
  const navigate = useNavigate();
  const appInit = useRef(false);

  // Derive formStatus from status
  const formStatus = status === "loading" ? "saving" : "idle";

  const formMethods = useForm({
    defaultValues: defaultFastTrackValues,
    mode: "onSubmit",
    reValidateMode: "onBlur",
    criteriaMode: "all",
  });

  useEffect(() => {
    if (appInit.current) return;
    // Only track if this is a new form (no appId)
    if (!appId) {
      if (fastTrackParams) {
        Object.keys(fastTrackParams).forEach((key) => {
          trackCustomEvent("form_field_filled", key, false);
        });
      }
      formMethods.reset({
        ...defaultFastTrackValues,
        ...savedFields,
        ...fastTrackParams,
      });
      appInit.current = true;
    }
  }, [appId, fastTrackParams, formMethods, savedFields]);

  // Handle error modal close
  const handleCloseErrorModal = () => {
    setShowErrorModal(false);
  };

  // Handle form submission
  const handleFormSubmit = async (data) => {
    try {
      // Use our fast track API hook to submit the application
      const result = await submitFastTrack(data);

      trackCustomEvent("fasttrack_form_submitted", true);

      // Store the application ID for the application form
      if (result.uuid) {
        storeAppID(result.uuid);

        // Redirect directly to application form page
        navigate(`/application/${result.uuid}`);
      } else {
        throw new Error(
          "No application ID returned from fast track submission"
        );
      }
    } catch (error) {
      // Show error modal
      logger.error("Error submitting fast track form:", error);
      setShowErrorModal(true);
    }
  };

  // Handle field blur events for tracking
  const handleOnBlur = async (event) => {
    const fieldName = event.target.name;

    if (!fieldName) return;

    const fieldsToUpdate = [fieldName];

    const values = formMethods.getValues();
    const oldValues = savedFields;

    for (const field of [
      "businessName",
      "firstName",
      "lastName",
      "email",
      "phone",
      "estimatedFICO",
      "consent",
    ]) {
      if (
        field !== fieldName &&
        values[field] &&
        values[field] !== oldValues[field]
      ) {
        fieldsToUpdate.push(field);
      }
    }

    await formMethods.trigger(fieldsToUpdate);

    fieldsToUpdate.forEach((field) => {
      const fieldHasError = formMethods.formState.errors[field];
      if (fieldHasError) {
        trackCustomEvent("form_field_error", field, false);
      } else {
        trackCustomEvent("form_field_filled", field, false);
      }
    });

    storeFormFields(values);
  };
  // Prepare props for the presentation component
  const presentationProps = {
    formMethods,
    showErrorModal,
    errorMessage,
    errorId,
    formStatus,
    handleOnBlur,
    handleCloseErrorModal,
    handleFormSubmit,
  };

  return <FastTrackFormPresentation {...presentationProps} />;
};

/**
 * Presentation component that handles only the UI rendering for the fast track form
 * @param {Object} props - Props passed from the container component
 * @returns {JSX.Element}
 */
const FastTrackFormPresentation = ({
  formMethods,
  showErrorModal,
  errorMessage,
  errorId,
  formStatus,
  handleCloseErrorModal,
  handleFormSubmit,
  handleOnBlur,
}) => {
  return (
    <div className="w-full">
      {/* Error Modal */}
      <ErrorModal
        isOpen={showErrorModal}
        error={errorMessage || "An error occurred. Please try again."}
        errorId={errorId || "UNKNOWN_ERROR"}
        onClose={handleCloseErrorModal}
      />

      {/* Loading Overlay */}
      {formStatus === "saving" && <LoadingOverlay />}

      <div className="bg-white rounded-lg shadow-lg p-6 sm:p-8">
        <div className="mb-6">
          <h2 className="text-4xl font-bold text-center text-gray-800 mb-6">
            Quick Business Funding
          </h2>
          <h3 className="text-xl font-semibold mb-4">Confirm your information</h3>
        </div>

        <form
          onSubmit={formMethods.handleSubmit(handleFormSubmit)}
          onBlur={handleOnBlur}
        >
          <div className="space-y-4">
            <FormField
              label="Business Name"
              name="businessName"
              type="text"
              placeholder="Acme LLC"
              control={formMethods.control}
              rules={validationSchema.businessName}
              data-hj-suppress
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 m-0">
              <FormField
                label="First Name"
                name="firstName"
                type="text"
                placeholder="John"
                control={formMethods.control}
                rules={validationSchema.firstName}
                data-hj-allow
              />

              <FormField
                label="Last Name"
                name="lastName"
                type="text"
                placeholder="Smith"
                control={formMethods.control}
                rules={validationSchema.lastName}
                data-hj-suppress
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 m-0">
              <FormField
                label="Email"
                name="email"
                type="email"
                inputMode="email"
                placeholder="<EMAIL>"
                control={formMethods.control}
                rules={validationSchema.email}
                data-hj-suppress
              />

              <PhoneField
                name="phone"
                label="Phone"
                type="tel"
                inputMode="tel"
                control={formMethods.control}
                rules={validationSchema.phone}
                data-hj-suppress
              />
            </div>

            <SelectField
              name="estimatedFICO"
              label="Estimated Credit Score"
              control={formMethods.control}
              options={ficoOptions}
              placeholder="Select Credit Score Range..."
              rules={validationSchema.estimatedFICO}
              data-hj-allow
            />

            <CheckboxField
              name="consent"
              label={
                <>
                  By continuing you are consenting to be contacted by phone,
                  SMS, and email from Pinnacle Funding. Message & data rates may
                  apply. You may OPT-OUT of communications at any time. See our{" "}
                  <a
                    href="https://pinnaclefundingco.com/privacy-policy/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline hover:text-blue-800 transition-colors"
                  >
                    Privacy Policy
                  </a>{" "}
                  and{" "}
                  <a
                    href="https://pinnaclefundingco.com/privacy-policy/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline hover:text-blue-800 transition-colors"
                  >
                    Terms
                  </a>
                  .
                </>
              }
              control={formMethods.control}
              rules={validationSchema.consent}
              data-hj-allow
            />
          </div>

          {/* Submit Button */}
          <div className="mt-8 flex justify-end">
            <button
              type="submit"
              disabled={formStatus === "saving"}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {formStatus === "saving" ? "Processing..." : "Continue"}
            </button>
          </div>
        </form>
      </div>
      <DataSecuredCard />
    </div>
  );
};
