import React, { useEffect, useState, useCallback, useRef } from "react";
import { logger } from "../../../utils/logger";
import { usePandaDocStatusApi } from "../../../hooks/usePandaDocStatusApi";

/**
 * Sign Application step component
 * This component displays a PandaDoc iframe for document signing
 *
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @param {Function} props.onDocumentSigned - Function to call when document is signed
 * @returns {JSX.Element}
 */
export const SignApplication = ({
  onDocumentSigned,
  sessionId,
  onDocumentLoaded,
  onRevisionsClick,
  appId,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const pollingIntervalRef = useRef(null);
  const onDocSignedCalled = useRef(false);

  const handleDocumentSignedEvent = useCallback(() => {
    if (onDocSignedCalled.current) return;
    onDocSignedCalled.current = true;
    onDocumentSigned();
  }, [onDocumentSigned]);

  const { checkDocumentStatus } = usePandaDocStatusApi();

  const checkStatus = useCallback(async () => {
    if (!appId || !sessionId) return;

    try {
      const statusResult = await checkDocumentStatus(appId);

      // If document is signed, automatically proceed to next step
      if (statusResult?.signed === true) {
        logger.log(
          "Document signed detected via polling, proceeding to next step"
        );
        // Clear polling interval
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        handleDocumentSignedEvent();
      }
    } catch (error) {
      logger.error("Error checking document status during polling:", error);
    }
  }, [appId, sessionId, checkDocumentStatus, handleDocumentSignedEvent]);

  const startPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    setTimeout(() => {
      if (pollingIntervalRef.current) return; // Don't start if already cleared

      checkStatus();

      pollingIntervalRef.current = setInterval(checkStatus, 2000);
    }, 2000);
  }, [checkStatus]);

  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, []);

  useEffect(() => {
    // Set up event listener for PandaDoc events
    const handlePandaDocEvent = (event) => {
      const { data } = event;
      if (!data) return;

      const { type, payload } = data;

      switch (type) {
        case "session_view.document.loaded":
          setIsLoading(false);
          onDocumentLoaded();
          // Start polling after 2 seconds when document is loaded
          startPolling();
          break;

        case "session_view.document.completed":
          // Stop polling when document is completed
          stopPolling();
          handleDocumentSignedEvent();
          break;

        case "session_view.document.exception":
          logger.error("Exception during document finalization", payload);
          setError("There was a problem with the document. Please try again.");
          break;

        default:
          break;
      }
    };

    window.addEventListener("message", handlePandaDocEvent);

    // Clean up event listener
    return () => {
      window.removeEventListener("message", handlePandaDocEvent);
      // Stop polling on cleanup
      stopPolling();
    };
  }, [
    sessionId,
    handleDocumentSignedEvent,
    onDocumentLoaded,
    startPolling,
    stopPolling,
  ]);

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold mb-4">Sign Your Application</h3>

      <p className="text-gray-600 mb-6">
        Please review and sign your application. For revisions{" "}
        <a
          onClick={() => {
            if (isLoading || !sessionId) return;
            onRevisionsClick();
          }}
          className="text-blue-600 hover:underline cursor-pointer"
        >
          click here
        </a>
        .
      </p>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 z-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        )}

        {/* PandaDoc iframe */}
        <div className="border border-gray-300 rounded-sm">
          <iframe
            src={`https://app.pandadoc.com/s/${sessionId}`}
            width="100%"
            height="800px"
            title="Sign Application"
            className="rounded-sm"
          ></iframe>
        </div>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        <p>
          By signing this document, you agree to the terms and conditions of the
          funding application.
        </p>
      </div>
    </div>
  );
};
